"""Application configuration settings."""

import os
from typing import Optional, List
from pydantic import Field, validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings with environment variable support."""

    # Application
    APP_NAME: str = Field(default="聊天系统", description="应用名称")
    APP_VERSION: str = Field(default="1.0.0", description="应用版本")
    DEBUG: bool = Field(default=False, env="DEBUG", description="调试模式")

    # Environment
    ENVIRONMENT: str = Field(default="development", env="ENVIRONMENT", description="运行环境")

    # Database
    DATABASE_URL: str = Field(
        default="sqlite:///./database.db",
        env="DATABASE_URL",
        description="数据库连接URL"
    )
    DB_POOL_SIZE: int = Field(default=20, env="DB_POOL_SIZE", description="数据库连接池大小")
    DB_MAX_OVERFLOW: int = Field(default=30, env="DB_MAX_OVERFLOW", description="数据库连接池最大溢出")
    DB_POOL_TIMEOUT: int = Field(default=30, env="DB_POOL_TIMEOUT", description="数据库连接超时时间")
    DB_POOL_RECYCLE: int = Field(default=3600, env="DB_POOL_RECYCLE", description="数据库连接回收时间")

    # Security
    SECRET_KEY: str = Field(
        default="your-secret-key-change-in-production-32chars-minimum",
        env="JWT_SECRET_KEY",
        description="JWT密钥"
    )
    ACCESS_TOKEN_EXPIRE_MINUTES: int = Field(
        default=60,
        env="ACCESS_TOKEN_EXPIRE_MINUTES",
        description="访问令牌过期时间(分钟)"
    )
    ALGORITHM: str = Field(default="HS256", description="JWT算法")
    ENCRYPTION_KEY: str = Field(
        default="your-encryption-key-change-in-production-32chars",
        env="ENCRYPTION_KEY",
        description="加密密钥"
    )

    # LDAP
    LDAP_URI: str = Field(
        default="ldap://**************:389",
        env="LDAP_URI",
        description="LDAP服务器地址"
    )
    LDAP_BASE_DN: str = Field(
        default="dc=users,dc=appdata,dc=erayt,dc=com",
        env="LDAP_BASE_DN",
        description="LDAP基础DN"
    )
    LDAP_TIMEOUT: int = Field(default=3, env="LDAP_TIMEOUT", description="LDAP连接超时时间")

    # CORS
    ALLOWED_ORIGINS: str = Field(
        default="http://localhost:3000,http://localhost:5173,http://localhost:8000,http://localhost:63342/",
        env="ALLOWED_ORIGINS",
        description="允许的跨域源（逗号分隔）"
    )

    # RAG
    MODEL_EMBEDDING_DIM: int = Field(default=1024, description="嵌入向量维度")
    MODEL_EMBEDDING: str = Field(default='bge-m3', env="MODEL_EMBEDDING", description="嵌入模型")
    MODEL_CHAT: str = Field(default='qwen3-32b', env="MODEL_CHAT", description="聊天模型")
    MODEL_RERANK: str = Field(default='bge-reranker-v2-m3', env="MODEL_RERANK", description="重排序模型")

    # API Keys (敏感信息通过环境变量获取)
    API_KEY: str = Field(
        default="sk-LLFZSeNxq59tCMCoA2Fc909a5a5d4720B0188556F67a7553",
        env="OPENAI_API_KEY",
        description="OpenAI API密钥"
    )
    API_URL: str = Field(
        default="http://**************:23000/v1",
        env="OPENAI_API_URL",
        description="OpenAI API地址"
    )
    API_RERANK_URL: str = Field(
        default="http://**************:9997/",
        env="RERANK_API_URL",
        description="重排序API地址"
    )

    # Milvus
    MILVUS_URL: str = Field(
        default="http://**************:19530",
        env="MILVUS_URL",
        description="Milvus地址"
    )
    MILVUS_USER: str = Field(
        default="dify",
        env="MILVUS_USER",
        description="Milvus用户名"
    )
    MILVUS_PASSWORD: str = Field(
        default="dify2025",
        env="MILVUS_PASSWORD",
        description="Milvus密码"
    )
    MILVUS_DB_NAME: str = Field(
        default="erayt_wiki",
        env="MILVUS_DB_NAME",
        description="Milvus db名称"
    )
    MILVUS_COLLECTION_NAME: str = Field(
        default="erayt_wiki",
        env="MILVUS_COLLECTION_NAME",
        description="Milvus集合名称"
    )
    MILVUS_CHUNK_STRATEGY: str = Field(
        default="parent_child",
        env="MILVUS_CHUNK_STRATEGY",
        description="Milvus分块策略"
    )

    # Rate Limiting
    RATE_LIMIT_REQUESTS: int = Field(default=100, env="RATE_LIMIT_REQUESTS", description="速率限制请求数")
    RATE_LIMIT_WINDOW: int = Field(default=60, env="RATE_LIMIT_WINDOW", description="速率限制时间窗口")

    model_config = {
        "env_file": ".env",
        "env_file_encoding": "utf-8",
        "case_sensitive": False,
        "extra": "ignore"
    }

    def get_allowed_origins(self) -> List[str]:
        """获取允许的跨域源列表"""
        if isinstance(self.ALLOWED_ORIGINS, str):
            if not self.ALLOWED_ORIGINS.strip():
                return ["http://localhost:3000", "http://localhost:5173"]
            return [origin.strip() for origin in self.ALLOWED_ORIGINS.split(',') if origin.strip()]
        return self.ALLOWED_ORIGINS

    @validator('SECRET_KEY', 'ENCRYPTION_KEY')
    def validate_keys(cls, v):
        if len(v) < 32:
            # 在开发环境中给出警告而不是错误
            import warnings
            warnings.warn(f'密钥长度建议至少32个字符，当前长度: {len(v)}')
        return v

    @property
    def is_production(self) -> bool:
        """判断是否为生产环境"""
        return self.ENVIRONMENT.lower() == "production"

    @property
    def is_development(self) -> bool:
        """判断是否为开发环境"""
        return self.ENVIRONMENT.lower() == "development"


def get_settings() -> Settings:
    """Get application settings instance."""
    return Settings()


# Global settings instance
settings = get_settings()
