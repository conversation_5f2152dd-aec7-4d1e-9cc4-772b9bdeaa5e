import logging
import traceback
from typing import List, Dict, Optional

from langchain_core.documents import Document
from langchain_core.retrievers import BaseRetriever
from pydantic import Field, PrivateAttr

from rag.vectorstore_search import VectorStore_Search, get_milvus_service_by_collection

# 配置日志
logger = logging.getLogger(__name__)


class RAGRetriever(BaseRetriever):
    """增强版RAG检索器，支持多种检索策略"""

    # Pydantic 字段定义
    collection_name: str = Field(description="集合名称")
    retrieval_config: Optional[Dict] = Field(default=None, description="检索配置参数")
    filter_conditions: Optional[Dict] = Field(default=None, description="过滤条件")

    # 私有属性，不参与 Pydantic 验证
    _milvus_service: Optional[VectorStore_Search] = PrivateAttr(default=None)
    _config: Dict = PrivateAttr(default_factory=dict)

    def __init__(
            self,
            collection_name: str,
            retrieval_config: Optional[Dict] = None,
            input: Dict = None,
            **kwargs
    ):
        """
        初始化RAG检索器
        """
        # 先调用父类初始化，传递 Pydantic 字段
        super().__init__(
            collection_name=collection_name,
            retrieval_config=retrieval_config,
            filter_conditions=input,
            **kwargs
        )

        # 初始化私有属性
        self._milvus_service = self._create_milvus_service(collection_name)
        self._config = retrieval_config or self._get_default_config()

        # 调试信息
        logger.debug(f"RAGRetriever 初始化完成，配置: {self._config}")

    @property
    def milvus_service(self) -> VectorStore_Search:
        """获取 Milvus 服务实例"""
        return self._milvus_service

    @property
    def config(self) -> Dict:
        """获取配置"""
        return self._config

    def _create_milvus_service(self, collection_name: str) -> VectorStore_Search:
        """创建Milvus服务实例（单例）"""
        return get_milvus_service_by_collection(collection_name)

    def _get_default_config(self) -> Dict:
        """获取默认检索配置"""
        return {
            "k": 5,
            "min_score": 0.1,
            "sort_by": "max_score",
            "enable_rerank": True,
            "rerank_top_k": 3,
            "max_content_length": 15000,
        }

    def _get_relevant_documents(self, query: str, **kwargs) -> List[Document]:
        """获取相关文档"""
        try:
            logger.info(f"开始检索文档: {query[:50]}...")
            logger.debug(f"当前配置: {self.config}")

            semantic_weight = 0.7
            if len(query) < 6:
                semantic_weight = 0.5

            # 1. 执行混合检索
            results = self.milvus_service.search_hybrid_parent_child(
                query=query,
                semantic_weight=semantic_weight,
                min_score=self.config["min_score"],
                sort_by=self.config["sort_by"],
                filter_conditions=self.filter_conditions
            )

            if not results:
                logger.warning("未找到相关文档")
                return []

            # 2. 转换为LangChain Document格式
            documents = []
            for i, result in enumerate(results):
                content = self._format_content(result, i + 1)
                metadata = self._extract_metadata(result, i)
                documents.append(Document(page_content=content, metadata=metadata))

            logger.info(f"检索到 {len(documents)} 个相关文档")

            # 3. 可选的重排序
            # if  len(documents) > 1:
            #     documents = self._rerank_documents(query, documents)

            return documents

        except Exception as e:
            logger.error(f"检索文档失败: {e}")
            return []

    def _format_content(self, result: Dict, ref_num: int) -> str:
        """格式化文档内容"""
        parent_doc = result.get("parent_document", "")
        parent_metadata = result.get("parent_metadata", {})
        search_stats = result.get("search_stats", {})

        filename = parent_metadata.get("filename", "未知文档")
        version = parent_metadata.get("version", "")
        score = search_stats.get("max_score", 0.0)

        # 构建格式化内容
        content_parts = [
            f"# 参考资料 {ref_num}",
            f"**来源**: {filename}",
        ]

        if version:
            content_parts.append(f"**版本**: {version}")

        content_parts.extend(
            [
                f"**相关度得分**: {score:.3f}",
                "",
                "**内容**:",
                "```",
                parent_doc,
                "```",
                "",
            ]
        )

        return "\n".join(content_parts)

    def _extract_metadata(self, result: Dict, rank: int) -> Dict:
        """提取文档元数据"""
        parent_metadata = result.get("parent_metadata", {})
        search_stats = result.get("search_stats", {})

        return {
            "source_id": parent_metadata.get("id", ""),
            "filename": parent_metadata.get("filename", ""),
            "version": parent_metadata.get("version", ""),
            "score": search_stats.get("max_score", 0.0),
            "rank": rank,
            "search_type": result.get("search_type", "hybrid_parent_child"),
            "hit_count": search_stats.get("hit_count", 0),
            "avg_score": search_stats.get("avg_score", 0.0),
            "total_score": search_stats.get("total_score", 0.0),
        }

    def _rerank_documents(
            self, query: str, documents: List[Document]
    ) -> List[Document]:
        """对文档进行重排序"""
        try:
            if hasattr(self.milvus_service, "rerank"):
                # 准备重排序数据
                docs_for_rerank = [
                    {
                        "document": doc.page_content,
                        "metadata": doc.metadata,
                        "score": doc.metadata.get("score", 0.0),
                    }
                    for doc in documents
                ]

                # 执行重排序
                reranked = self.milvus_service.rerank(
                    query=query,
                    documents=docs_for_rerank,
                    top_k=3,
                )

                # 转换回Document格式
                return [
                    Document(page_content=item["document"], metadata=item["metadata"])
                    for item in reranked
                ]

            return documents[: self.config["rerank_top_k"]]

        except Exception as e:
            logger.warning(f"重排序失败，使用原始结果: {traceback.print_exc()}")
            return documents
