## 过滤条件设计文档

### 1. 需求概述

根据现有需求，用户在新建对话后需要在聊天页面选择知识库(collection_name)，某些知识库还需要选择额外的过滤条件（如class_tag）。这些信息需要在聊天请求中传递给后端，以便进行精准的检索。

根据新要求，知识库集合和过滤条件（如class_tag）等需要通过后端配置，然后使用fastapi接口传递给前端。只需要一个接口返回所有信息，格式按照指定的样式。

### 2. 系统分析

#### 2.1 当前系统架构

通过分析代码，我们了解到当前系统架构如下：

1. 前端使用Vue.js构建聊天界面，通过API与后端交互
2. 后端使用FastAPI提供RESTful API接口
3. 聊天请求通过[ChatRequest](file:///D:/code/chatbot-ui/chatbot/backend/schemas/message.py#L32-L84)数据模型进行验证
4. RAG检索通过[RAGRetriever](file:///D:/code/chatbot-ui/chatbot/backend/rag/rag_retriever.py#L16-L31)类实现，支持过滤条件
5. 过滤条件通过[VectorStore_Search](file:///D:/code/chatbot-ui/chatbot/backend/rag/vectorstore_search.py#L24-L114)类构建Milvus查询表达式

#### 2.2 当前数据模型

当前[ChatRequest](file:///D:/code/chatbot-ui/chatbot/backend/schemas/message.py#L32-L84)模型包含以下字段：
- collection_name: str - 知识库集合名称（已存在）
- conversation_id: int - 会话ID
- message: str - 用户消息内容
- input: Dict[str, Any] - 额外输入参数（可用于传递过滤条件）
- parent_msg_id: int - 父消息ID

#### 2.3 当前RAG实现

当前系统已经支持通过[input](file:///D:/code/chatbot-ui/chatbot/backend/schemas/message.py#L53-L57)参数传递过滤条件到RAG检索器，并通过[build_filter_expr](file:///D:/code/chatbot-ui/chatbot/backend/rag/vectorstore_search.py#L74-L134)方法构建Milvus查询表达式。

### 3. 设计方案

#### 3.1 后端配置管理

为了满足新的需求，我们需要在后端添加配置管理功能：

1. **知识库集合配置**：
   - 在配置文件中添加可配置的知识库集合列表
   - 为每个知识库配置其过滤条件项和可选项
   - 提供API接口获取所有知识库及其过滤条件

#### 3.2 新增API接口

需要新增以下API接口：

1. `GET /api/collections/config` - 获取所有知识库及其过滤条件配置

#### 3.3 数据模型设计

1. 保持现有[ChatRequest](file:///D:/code/chatbot-ui/chatbot/backend/schemas/message.py#L32-L84)模型不变
2. 利用现有的[input](file:///D:/code/chatbot-ui/chatbot/backend/schemas/message.py#L53-L57)字段传递过滤条件

#### 3.4 前端实现方案

1. 页面加载时调用API获取所有知识库及其过滤条件配置
2. 根据返回数据显示知识库选择下拉框
3. 当用户选择知识库时，动态显示该知识库对应的过滤条件项
4. 在发送消息时，将选择的collection_name和过滤条件通过[input](file:///D:/code/chatbot-ui/chatbot/backend/schemas/message.py#L53-L57)参数传递

### 4. 详细实现步骤

#### 4.1 后端实现

1. **配置管理**：
   - 在[settings.py](file:///D:/code/chatbot-ui/chatbot/backend/config/settings.py)中添加知识库集合和过滤条件配置
   - 添加配置示例：
     ```python
     # 知识库集合配置
     COLLECTIONS_CONFIG: Dict[str, Dict] = {
         "FinancialResearchOffice": {
             "display_name": "金融研究室",
             "description": "金融研究室的知识库集合",
             "filter": [
                 {
                     "name": "class_tag",
                     "display_name": "class_tag",
                     "description": "class_tag",
                     "options": ["公共", "估值"]
                 },
                 {
                     "name": "version",
                     "display_name": "版本",
                     "description": "文档版本",
                     "options": ["V5.0", "ALL"]
                 }
             ]
         },
         "rcs": {
             "display_name": "RCS知识库",
             "description": "RCS相关文档",
             "filter": []
         }
     }
     ```

2. **新增API接口**：
   - 在[api](file:///D:/code/chatbot-ui/chatbot/backend/api/)目录下创建[collections.py](file:///D:/code/chatbot-ui/chatbot/backend/api/conversations.py)文件，实现以下接口：
     - `GET /api/collections/config` - 返回所有知识库及其过滤条件配置

3. **接口实现示例**：
   ```python
   from fastapi import APIRouter, Depends
   from typing import List, Dict
   from schemas.response import CollectionConfigListResponse
   from config.settings import settings
   
   router = APIRouter(prefix="/collections", tags=["collections"])
   
   @router.get("/config", response_model=CollectionConfigListResponse)
   async def get_collections_config():
       """获取所有知识库及其过滤条件配置"""
       collections = []
       for key, value in settings.COLLECTIONS_CONFIG.items():
           collection_config = {
               "name": key,
               "display_name": value["display_name"],
               "description": value["description"],
               "filter": value.get("filter", [])
           }
           collections.append(collection_config)
       
       return {
           "code": 200,
           "message": "success",
           "data": collections
       }
   ```

#### 4.2 前端实现

1. **页面初始化**：
   - 点击新建对话之后，页面加载时调用`GET /api/collections/config`接口获取所有知识库及其过滤条件配置
   - 根据返回数据显示知识库选择下拉框

2. **知识库选择处理**：
   - 当用户选择知识库时，根据该知识库的配置动态显示过滤条件项
   - 为每个过滤条件项生成对应的输入控件（如下拉框）

3. **消息发送处理**：
   - 在发送消息时，将选择的collection_name和所有过滤条件添加到[input](file:///D:/code/chatbot-ui/chatbot/backend/schemas/message.py#L53-L57)参数中
   - 保持现有API调用接口不变

4. **前端代码示例**：
   ```javascript
   // 获取知识库配置
   const loadCollectionsConfig = async () => {
       try {
           const result = await apiService.getCollectionsConfig();
           if (result.code === 200) {
               collectionsConfig.value = result.data;
           } else {
               console.error('获取知识库配置失败:', result.message);
           }
       } catch (error) {
           console.error('获取知识库配置错误:', error);
       }
   };
   
   // 知识库选择处理
   const handleCollectionChange = (collectionName) => {
       selectedCollection.value = collectionName;
       // 根据选择的知识库显示对应的过滤条件
       const collection = collectionsConfig.value.find(c => c.name === collectionName);
       if (collection && collection.filter) {
           filterConfig.value = collection.filter;
       } else {
           filterConfig.value = [];
       }
       // 重置已选择的过滤条件
       selectedFilters.value = {};
   };
   ```

### 5. API设计

#### 5.1 获取知识库及其过滤条件配置
```
GET /api/collections/config

Response:
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "name": "default",
      "display_name": "默认知识库",
      "description": "默认的知识库集合",
      "filter": [
        {
          "name": "class_tag",
          "display_name": "class_tag",
          "description": "class_tag",
          "options": [
            "公共",
            "估值"
          ]
        },
        {
          "name": "version",
          "display_name": "版本",
          "description": "文档版本",
          "options": [
            "V5.0",
            "ALL"
          ]
        }
      ]
    },
    {
      "name": "rcs",
      "display_name": "RCS知识库",
      "description": "RCS相关文档",
      "filter": []
    }
  ]
}
```

#### 5.2 聊天接口保持不变
```
POST /chat/stream
Content-Type: application/json

{
  "collection_name": "default",
  "conversation_id": 123,
  "message": "用户问题",
  "input": {
    "class_tag": "公共",
    "version": "V5.0"
  },
  "parent_msg_id": 0
}
```

### 6. 安全验证

1. **后端验证**：
   - 验证请求的collection_name是否在配置列表中
   - 验证过滤条件项是否在允许的列表中
   - 验证过滤条件值是否在允许的选项中
   - 利用Pydantic模型验证[ChatRequest](file:///D:/code/chatbot-ui/chatbot/backend/schemas/message.py#L32-L84)参数

2. **前端验证**：
   - 确保用户只能从获取到的列表中选择
   - 验证输入的参数格式

### 7. 错误处理

1. 知识库不存在：
   - 后端应验证知识库是否存在
   - 返回404错误或相应的错误码

2. 过滤条件错误：
   - 后端应捕获过滤条件构建过程中的异常
   - 返回友好的错误信息给前端

3. 网络错误：
   - 前端应处理API调用失败的情况
   - 提供重试机制或友好的错误提示

### 8. 测试方案

1. 功能测试：
   - 验证知识库配置获取接口
   - 验证知识库选择功能
   - 验证过滤条件项的动态显示
   - 验证过滤条件的选择和传递
   - 验证过滤条件正确传递到后端
   - 验证检索结果符合过滤条件

2. 边界测试：
   - 测试未选择知识库时无法发送消息
   - 测试选择不同知识库的检索结果差异
   - 测试错误过滤条件的处理
   - 测试不存在的知识库或过滤条件的处理

### 9. 部署建议

1. 在配置文件中正确配置知识库集合和过滤条件信息
2. 确保新增的API接口正确注册到主应用中
3. 部署时验证所有配置项的正确性

### 10. 未来扩展性

1. 可以支持从数据库动态加载知识库配置
2. 可以添加管理界面来动态配置知识库和过滤条件
3. 可以支持更复杂的过滤条件配置（如多级联动）
4. 可以根据用户权限返回不同的知识库列表
5. 可以支持过滤条件的默认值设置