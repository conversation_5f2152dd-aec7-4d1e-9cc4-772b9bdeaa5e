"""LLM服务实现，集成RAG问答链路"""

import logging
import traceback
from datetime import datetime
from functools import lru_cache
from typing import List, Generator, Dict, Tuple, Optional
from langchain_core.documents import Document
from langchain_core.output_parsers import StrOutputParser
from langchain_core.prompts import Chat<PERSON>romptTemplate
from langchain_core.runnables import RunnablePassthrough
from langchain_openai import ChatOpenAI

from config.settings import settings
from rag.rag_retriever import RAGRetriever

# 配置日志
logger = logging.getLogger(__name__)


class LLMService:
    """LLM服务，负责RAG问答链路"""

    def __init__(self):
        """初始化LLM服务"""
        self.llm = self._create_llm()

    def _create_llm(self) -> ChatOpenAI:
        """创建LLM实例"""
        return ChatOpenAI(
            model=settings.MODEL_CHAT,
            api_key=settings.API_KEY,
            base_url=settings.API_URL,
            temperature=0.7,
            streaming=True,
            max_tokens=2000,
        )


    def _format_context(self, documents: List[Document]) -> str:
        """格式化上下文"""
        if not documents:
            return "暂无相关参考资料。"

        context_parts = []
        for i, doc in enumerate(documents, 1):
            context_parts.append(f"## 参考资料 {i}")
            context_parts.append(doc.page_content)
            context_parts.append("")  # 空行分隔

        return "\n".join(context_parts)

    def _extract_references_from_documents(self, documents: List[Document]) -> List[Dict]:
        """从文档中提取引用信息"""
        references = []
        for i, doc in enumerate(documents, 1):
            metadata = doc.metadata
            references.append({
                "id": i,
                "title": metadata.get("filename", f"参考资料 {i}"),
                "content": doc.page_content,
                "source": metadata.get("source", ""),
                "score": metadata.get("score", 0.0)
            })
        return references

    def generate_rag_response_with_references(self, message: str, collection_name: str, input: Dict = None) -> Tuple[Generator[str, None, None], List[Dict]]:
        """
        生成流式聊天回复并返回引用内容
        返回: (content_generator, references_list)
        """
        try:
            logger.info(f"开始生成回复: {message[:50]}...")

            # 1. 先获取相关文档
            retriever = RAGRetriever(collection_name, input)
            documents = retriever._get_relevant_documents(message)

            # 2. 提取引用信息
            references = self._extract_references_from_documents(documents)

            # 3. 创建RAG链
            def _create_rag_chain():
                system_prompt = """你是一个专业的智能助手，请根据以下参考资料回答用户的问题。

        参考资料：
        {context}

        请根据上述参考资料回答用户问题。回答时请注意：
        1. 优先使用参考资料中的信息
        2. 如果参考资料不足以回答问题，请直接回复：无法检索到相关内容，请换个方式再提问试试吧！
        3. 保持回答的准确性和完整性
        4. 适当引用资料来源
        5. 使用简洁明了的语言

        用户问题：{question}

        回答："""

                prompt_template = ChatPromptTemplate.from_template(system_prompt)
                context = self._format_context(documents)

                # 构建RAG链
                rag_chain = (
                        {
                            "context": lambda _: context,
                            "question": RunnablePassthrough(),
                        }
                        | prompt_template
                        | self.llm
                        | StrOutputParser()
                )
                return rag_chain

            # 4. 生成流式回复
            def content_generator():
                try:
                    rag_chain = _create_rag_chain()
                    for chunk in rag_chain.stream(message):
                        if chunk:
                            yield chunk
                except Exception as e:
                    logger.error(f"生成回复失败: {str(e)}")
                    import time
                    error_message = f"抱歉，RAG服务暂时不可用：{str(e)}。"
                    words = error_message.split()
                    for word in words:
                        yield word + " "
                        time.sleep(0.1)

            return content_generator(), references

        except Exception as e:
            logger.error(f"生成回复失败: {str(e)}")
            # 如果RAG失败，提供备用回复
            def error_generator():
                import time
                error_message = f"抱歉，RAG服务暂时不可用：{str(e)}。"
                words = error_message.split()
                for word in words:
                    yield word + " "
                    time.sleep(0.1)

            return error_generator(), []

    def generate_rag_response(self, message: str, collection_name: str, input: Dict = None) -> Generator[
        str, None, None]:
        """
        生成流式聊天回复（保持向后兼容）
        """
        content_generator, _ = self.generate_rag_response_with_references(message, collection_name, input)
        yield from content_generator


# 创建全局LLM服务实例
@lru_cache(maxsize=1)
def get_llm_service() -> LLMService:
    """获取LLM服务实例（单例模式）"""
    return LLMService()
